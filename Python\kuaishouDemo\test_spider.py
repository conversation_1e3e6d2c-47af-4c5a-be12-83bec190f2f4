#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手小店爬虫测试脚本
"""

import unittest
import os
import json
import tempfile
from unittest.mock import Mock, patch, MagicMock
from kuaishou_spider import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ProductInfo
from anti_crawler import AntiCrawlerManager, UserAgentRotator, RateLimiter

class TestKuaishouSpider(unittest.TestCase):
    """快手小店爬虫测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config = {
            "headers": {"User-Agent": "test-agent"},
            "request_delay": [1, 2],
            "max_retries": 2,
            "timeout": 10,
            "output_format": "csv",
            "output_file": "test_products.csv"
        }
        
        # 创建临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_config, self.temp_config)
        self.temp_config.close()
        
        self.spider = <PERSON><PERSON>hou<PERSON>pider(self.temp_config.name)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_config.name):
            os.unlink(self.temp_config.name)
        
        # 清理测试生成的文件
        test_files = [
            "test_products.csv",
            "test_products.json",
            "test_products.xlsx",
            "summary_report.json",
            "error_log.json"
        ]
        
        for file in test_files:
            if os.path.exists(file):
                os.unlink(file)
    
    def test_load_config(self):
        """测试配置加载"""
        self.assertEqual(self.spider.config["max_retries"], 2)
        self.assertEqual(self.spider.config["timeout"], 10)
        self.assertEqual(self.spider.config["output_format"], "csv")
    
    def test_extract_product_id(self):
        """测试商品ID提取"""
        test_cases = [
            ("https://shop.kuaishou.com/product/123456", "123456"),
            ("https://shop.kuaishou.com/goods/789012", "789012"),
            ("https://example.com/item/345678", "345678"),
        ]
        
        for url, expected_id in test_cases:
            with self.subTest(url=url):
                result = self.spider._extract_product_id(url, "")
                self.assertEqual(result, expected_id)
    
    def test_extract_title(self):
        """测试标题提取"""
        html_content = '<title>iPhone 15 Pro Max - 快手小店</title>'
        result = self.spider._extract_title(html_content)
        self.assertIn("iPhone 15 Pro Max", result)
    
    def test_extract_prices(self):
        """测试价格提取"""
        html_content = '''
        <div class="price">¥8999.00</div>
        <div class="original-price">原价: ¥9999.00</div>
        '''
        
        price, original_price = self.spider._extract_prices(html_content)
        self.assertEqual(price, 8999.0)
        self.assertEqual(original_price, 9999.0)
    
    def test_clean_text(self):
        """测试文本清理"""
        dirty_text = "<p>iPhone 15   Pro Max</p>\n\n手机"
        clean_text = self.spider._clean_text(dirty_text)
        self.assertEqual(clean_text, "iPhone 15 Pro Max 手机")
    
    def test_data_validation(self):
        """测试数据验证"""
        # 创建测试商品数据
        valid_product = ProductInfo(
            product_id="123456",
            title="Test Product",
            price=99.99,
            original_price=None,
            sales_count=100,
            rating=4.5,
            review_count=50,
            shop_name="Test Shop",
            product_url="https://shop.kuaishou.com/product/123456",
            image_urls=["https://example.com/image.jpg"],
            description="Test description",
            category="Test Category",
            brand="Test Brand",
            stock_status="有货",
            crawl_time="2025-07-16 10:00:00"
        )
        
        invalid_product = ProductInfo(
            product_id="",  # 无效ID
            title="",       # 无效标题
            price=-10,      # 无效价格
            original_price=None,
            sales_count=-5, # 无效销量
            rating=6.0,     # 无效评分
            review_count=0,
            shop_name="Test Shop",
            product_url="invalid-url",  # 无效URL
            image_urls=[],
            description="",
            category="",
            brand="",
            stock_status="",
            crawl_time="2025-07-16 10:00:00"
        )
        
        products = [valid_product, invalid_product]
        cleaned_products = self.spider.clean_and_validate_data(products)
        
        # 应该只有一个有效商品
        self.assertEqual(len(cleaned_products), 1)
        self.assertEqual(cleaned_products[0].product_id, "123456")
    
    def test_generate_summary_report(self):
        """测试摘要报告生成"""
        products = [
            ProductInfo(
                product_id="1", title="Product 1", price=100.0, original_price=None,
                sales_count=50, rating=4.0, review_count=10, shop_name="Shop A",
                product_url="https://example.com/1", image_urls=[], description="",
                category="", brand="", stock_status="", crawl_time="2025-07-16 10:00:00"
            ),
            ProductInfo(
                product_id="2", title="Product 2", price=200.0, original_price=None,
                sales_count=30, rating=4.5, review_count=20, shop_name="Shop B",
                product_url="https://example.com/2", image_urls=[], description="",
                category="", brand="", stock_status="", crawl_time="2025-07-16 10:00:00"
            )
        ]
        
        report = self.spider.generate_summary_report(products)
        
        self.assertEqual(report['total_products'], 2)
        self.assertEqual(report['total_sales'], 80)
        self.assertEqual(report['average_price'], 150.0)
        self.assertEqual(report['average_rating'], 4.25)
    
    @patch('requests.Session.get')
    def test_make_request_success(self, mock_get):
        """测试成功请求"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<html>Test content</html>"
        mock_response.raise_for_status.return_value = None
        
        self.spider.anti_crawler.make_safe_request = Mock(return_value=mock_response)
        
        result = self.spider.make_request("https://example.com")
        self.assertIsNotNone(result)
        self.assertEqual(result.text, "<html>Test content</html>")
    
    def test_save_to_csv(self):
        """测试CSV保存"""
        products = [
            ProductInfo(
                product_id="1", title="Test Product", price=99.99, original_price=None,
                sales_count=100, rating=4.5, review_count=50, shop_name="Test Shop",
                product_url="https://example.com/1", image_urls=["img1.jpg"], description="Test",
                category="Category", brand="Brand", stock_status="有货", crawl_time="2025-07-16 10:00:00"
            )
        ]
        
        self.spider.save_to_csv(products, "test_products.csv")
        self.assertTrue(os.path.exists("test_products.csv"))
        
        # 检查文件内容
        with open("test_products.csv", 'r', encoding='utf-8-sig') as f:
            content = f.read()
            self.assertIn("Test Product", content)
            self.assertIn("99.99", content)
    
    def test_save_to_json(self):
        """测试JSON保存"""
        products = [
            ProductInfo(
                product_id="1", title="Test Product", price=99.99, original_price=None,
                sales_count=100, rating=4.5, review_count=50, shop_name="Test Shop",
                product_url="https://example.com/1", image_urls=["img1.jpg"], description="Test",
                category="Category", brand="Brand", stock_status="有货", crawl_time="2025-07-16 10:00:00"
            )
        ]
        
        self.spider.save_to_json(products, "test_products.json")
        self.assertTrue(os.path.exists("test_products.json"))
        
        # 检查文件内容
        with open("test_products.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            self.assertEqual(len(data), 1)
            self.assertEqual(data[0]['title'], "Test Product")


class TestAntiCrawler(unittest.TestCase):
    """反爬虫模块测试类"""
    
    def test_user_agent_rotator(self):
        """测试User-Agent轮换"""
        rotator = UserAgentRotator()
        ua1 = rotator.get_random_ua()
        ua2 = rotator.get_random_ua()
        
        self.assertIsInstance(ua1, str)
        self.assertIsInstance(ua2, str)
        self.assertTrue(len(ua1) > 0)
        self.assertTrue(len(ua2) > 0)
    
    def test_rate_limiter(self):
        """测试频率限制器"""
        limiter = RateLimiter(max_requests=2, time_window=60)
        
        # 前两个请求应该被允许
        self.assertTrue(limiter.can_make_request())
        limiter.record_request()
        
        self.assertTrue(limiter.can_make_request())
        limiter.record_request()
        
        # 第三个请求应该被拒绝
        self.assertFalse(limiter.can_make_request())


def run_tests():
    """运行所有测试"""
    print("开始运行快手小店爬虫测试...")
    print("=" * 50)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestKuaishouSpider))
    suite.addTests(loader.loadTestsFromTestCase(TestAntiCrawler))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 50)
    if result.wasSuccessful():
        print("所有测试通过！✅")
    else:
        print(f"测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误 ❌")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)

# 快手小店爬虫使用指南

## 项目结构

```
kuaishouDemo/
├── README.md              # 项目说明文档
├── USAGE.md              # 使用指南（本文件）
├── requirements.txt      # 依赖包列表
├── config.json          # 配置文件
├── kuaishou_spider.py   # 主爬虫模块
├── anti_crawler.py      # 反爬虫处理模块
├── utils.py             # 工具函数模块
├── run_spider.py        # 命令行运行脚本
├── example.py           # 使用示例脚本
└── test_spider.py       # 测试脚本
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 创建配置文件

```bash
python run_spider.py --create-config
```

### 3. 运行爬虫

```bash
# 基本使用
python run_spider.py "https://shop.kuaishou.com/profile/123456789"

# 指定页数和格式
python run_spider.py "https://shop.kuaishou.com/profile/123456789" --pages 5 --format excel

# 使用代理
python run_spider.py "https://shop.kuaishou.com/profile/123456789" --proxy "http://proxy.example.com:8080"
```

## 详细使用方法

### 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `shop_url` | 店铺URL（必需） | `"https://shop.kuaishou.com/profile/123456789"` |
| `--pages` | 爬取页数 | `--pages 5` |
| `--format` | 输出格式 | `--format excel` |
| `--output` | 输出文件名 | `--output my_products.csv` |
| `--delay` | 请求延时范围 | `--delay 2 5` |
| `--retries` | 最大重试次数 | `--retries 3` |
| `--timeout` | 请求超时时间 | `--timeout 30` |
| `--proxy` | 代理服务器 | `--proxy "http://proxy:8080"` |
| `--verbose` | 详细输出 | `--verbose` |
| `--create-config` | 创建配置文件 | `--create-config` |

### 编程接口使用

```python
from kuaishou_spider import KuaishouSpider

# 创建爬虫实例
spider = KuaishouSpider()

# 方法1: 直接运行
spider.run("https://shop.kuaishou.com/profile/123456789", max_pages=3)

# 方法2: 分步骤执行
products = spider.crawl_shop_products("https://shop.kuaishou.com/profile/123456789", max_pages=3)
cleaned_products = spider.clean_and_validate_data(products)
spider.save_to_csv(cleaned_products, "products.csv")
spider.save_summary_report(cleaned_products)
```

## 配置选项详解

### 基本配置

```json
{
  "output_format": "csv",           // 输出格式: csv, json, excel
  "output_file": "kuaishou_products.csv",
  "max_retries": 3,                 // 最大重试次数
  "timeout": 30,                    // 请求超时时间（秒）
  "request_delay": [2, 5],          // 请求延时范围（秒）
  "max_consecutive_failures": 10    // 最大连续失败次数
}
```

### 请求头配置

```json
{
  "headers": {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9...",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive"
  }
}
```

### 代理配置

```json
{
  "enable_proxy": true,
  "proxies": [
    "http://proxy1.example.com:8080",
    "http://proxy2.example.com:8080"
  ]
}
```

### 爬取设置

```json
{
  "crawl_settings": {
    "max_pages_per_shop": 10,
    "max_products_per_page": 20,
    "enable_image_download": false,
    "image_download_path": "./images/",
    "enable_description_crawl": true,
    "enable_review_crawl": false
  }
}
```

## 输出文件说明

### 商品数据文件

- **CSV格式**: `kuaishou_products.csv` - 表格形式，适合Excel打开
- **JSON格式**: `kuaishou_products.json` - 结构化数据，适合程序处理
- **Excel格式**: `kuaishou_products.xlsx` - Excel文件，包含中文列名

### 摘要报告

`summary_report.json` 包含：
- 商品总数和总销量
- 平均价格和评分
- 价格分布统计
- 销量分布统计
- 店铺排行榜

### 日志文件

- `kuaishou_spider.log` - 运行日志
- `error_log.json` - 错误记录

## 常见使用场景

### 1. 单店铺数据采集

```bash
python run_spider.py "https://shop.kuaishou.com/profile/123456789" --pages 10 --format excel
```

### 2. 批量店铺数据采集

```python
from kuaishou_spider import KuaishouSpider

shop_urls = [
    "https://shop.kuaishou.com/profile/123456789",
    "https://shop.kuaishou.com/profile/987654321"
]

spider = KuaishouSpider()
all_products = []

for shop_url in shop_urls:
    products = spider.crawl_shop_products(shop_url, max_pages=5)
    all_products.extend(products)

spider.save_to_excel(all_products, "all_shops_products.xlsx")
```

### 3. 数据分析

```python
from kuaishou_spider import KuaishouSpider

spider = KuaishouSpider()
products = spider.crawl_shop_products(shop_url, max_pages=5)
cleaned_products = spider.clean_and_validate_data(products)

# 生成分析报告
report = spider.generate_summary_report(cleaned_products)
print(f"平均价格: ¥{report['average_price']}")
print(f"总销量: {report['total_sales']}")
```

### 4. 定时任务

```python
import schedule
import time
from kuaishou_spider import KuaishouSpider

def crawl_job():
    spider = KuaishouSpider()
    spider.run("https://shop.kuaishou.com/profile/123456789", max_pages=3)

# 每天上午10点执行
schedule.every().day.at("10:00").do(crawl_job)

while True:
    schedule.run_pending()
    time.sleep(60)
```

## 故障排除

### 常见问题

1. **请求被拒绝/验证码**
   - 增加请求延时: `--delay 5 10`
   - 使用代理: `--proxy "http://proxy:8080"`
   - 减少并发请求

2. **数据不完整**
   - 检查网络连接
   - 更新User-Agent
   - 检查目标网站结构变化

3. **程序崩溃**
   - 查看日志文件: `kuaishou_spider.log`
   - 使用详细模式: `--verbose`
   - 检查依赖包版本

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger().setLevel(logging.DEBUG)

# 查看请求统计
spider = KuaishouSpider()
stats = spider.get_crawl_statistics()
print(stats)

# 单步调试
products = spider.crawl_shop_products(shop_url, max_pages=1)
print(f"获取到 {len(products)} 个商品")
```

## 性能优化

### 1. 调整请求频率

```json
{
  "request_delay": [1, 2],    // 快速模式
  "request_delay": [3, 6]     // 安全模式
}
```

### 2. 使用代理池

```json
{
  "enable_proxy": true,
  "proxies": [
    "http://proxy1:8080",
    "http://proxy2:8080",
    "http://proxy3:8080"
  ]
}
```

### 3. 批量处理

```python
# 批量保存，减少I/O操作
products = []
for shop_url in shop_urls:
    products.extend(spider.crawl_shop_products(shop_url))

spider.save_to_csv(products, "batch_results.csv")
```

## 注意事项

1. **合法合规**: 确保爬取行为符合网站服务条款和相关法律法规
2. **频率控制**: 合理控制请求频率，避免对服务器造成压力
3. **数据使用**: 爬取的数据仅供学习和研究使用
4. **及时更新**: 网站结构可能变化，需要及时更新解析规则

## 技术支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 运行测试脚本检查环境: `python test_spider.py`
3. 查看示例代码: `python example.py`
4. 提交Issue到项目仓库

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具模块
包含各种辅助函数和工具类
"""

import re
import json
import hashlib
import urllib.parse
from typing import Dict, List, Optional, Any
import requests
from datetime import datetime
import os


class URLUtils:
    """URL处理工具类"""
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """检查URL是否有效"""
        try:
            result = urllib.parse.urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
    
    @staticmethod
    def normalize_url(url: str, base_url: str = "") -> str:
        """标准化URL"""
        if url.startswith('http'):
            return url
        elif url.startswith('//'):
            return 'https:' + url
        elif url.startswith('/'):
            if base_url:
                parsed_base = urllib.parse.urlparse(base_url)
                return f"{parsed_base.scheme}://{parsed_base.netloc}{url}"
            return url
        else:
            if base_url:
                return urllib.parse.urljoin(base_url, url)
            return url
    
    @staticmethod
    def extract_domain(url: str) -> str:
        """提取域名"""
        try:
            parsed = urllib.parse.urlparse(url)
            return parsed.netloc
        except:
            return ""


class TextUtils:
    """文本处理工具类"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()（）【】""''¥￥%]', '', text)
        
        return text.strip()
    
    @staticmethod
    def extract_numbers(text: str) -> List[float]:
        """从文本中提取数字"""
        pattern = r'\d+\.?\d*'
        matches = re.findall(pattern, text)
        return [float(match) for match in matches]
    
    @staticmethod
    def extract_price(text: str) -> Optional[float]:
        """从文本中提取价格"""
        # 匹配价格模式：¥123.45, ￥123, 123.45元等
        price_patterns = [
            r'[¥￥]\s*(\d+\.?\d*)',
            r'(\d+\.?\d*)\s*元',
            r'价格[：:]\s*(\d+\.?\d*)',
            r'售价[：:]\s*(\d+\.?\d*)'
        ]
        
        for pattern in price_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    return float(match.group(1))
                except:
                    continue
        
        return None
    
    @staticmethod
    def extract_sales_count(text: str) -> Optional[int]:
        """从文本中提取销量"""
        sales_patterns = [
            r'销量[：:]\s*(\d+)',
            r'已售[：:]\s*(\d+)',
            r'月销[：:]\s*(\d+)',
            r'(\d+)\s*人付款',
            r'(\d+)\s*笔交易'
        ]
        
        for pattern in sales_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    return int(match.group(1))
                except:
                    continue
        
        return None


class DataValidator:
    """数据验证工具类"""
    
    @staticmethod
    def validate_product_data(product_data: Dict) -> bool:
        """验证商品数据的完整性"""
        required_fields = ['product_id', 'title', 'price']
        
        for field in required_fields:
            if field not in product_data or not product_data[field]:
                return False
        
        # 验证价格是否为有效数字
        try:
            float(product_data['price'])
        except (ValueError, TypeError):
            return False
        
        return True
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        filename = re.sub(illegal_chars, '_', filename)
        
        # 限制长度
        if len(filename) > 200:
            filename = filename[:200]
        
        return filename.strip()


class FileUtils:
    """文件处理工具类"""
    
    @staticmethod
    def ensure_dir(directory: str):
        """确保目录存在"""
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    @staticmethod
    def get_file_size(filepath: str) -> int:
        """获取文件大小（字节）"""
        try:
            return os.path.getsize(filepath)
        except:
            return 0
    
    @staticmethod
    def backup_file(filepath: str) -> str:
        """备份文件"""
        if not os.path.exists(filepath):
            return ""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{filepath}.backup_{timestamp}"
        
        try:
            import shutil
            shutil.copy2(filepath, backup_path)
            return backup_path
        except:
            return ""


class HashUtils:
    """哈希工具类"""
    
    @staticmethod
    def md5_hash(text: str) -> str:
        """计算MD5哈希值"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    @staticmethod
    def generate_product_id(url: str, title: str) -> str:
        """生成商品唯一ID"""
        combined = f"{url}_{title}"
        return HashUtils.md5_hash(combined)[:16]


class RequestUtils:
    """请求工具类"""
    
    @staticmethod
    def get_random_user_agent() -> str:
        """获取随机User-Agent"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        ]
        
        import random
        return random.choice(user_agents)
    
    @staticmethod
    def is_blocked_response(response: requests.Response) -> bool:
        """检查响应是否被反爬虫拦截"""
        # 检查状态码
        if response.status_code in [403, 429, 503]:
            return True
        
        # 检查响应内容中的关键词
        blocked_keywords = [
            '验证码', 'captcha', '人机验证', '访问频繁',
            'blocked', 'forbidden', 'rate limit'
        ]
        
        content = response.text.lower()
        for keyword in blocked_keywords:
            if keyword in content:
                return True
        
        return False


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """加载配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    
    def save_config(self, config: Dict):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save_config(self.config)


class RateLimiter:
    """请求频率限制器"""
    
    def __init__(self, max_requests: int = 10, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    def can_make_request(self) -> bool:
        """检查是否可以发起请求"""
        now = datetime.now().timestamp()
        
        # 清理过期的请求记录
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.time_window]
        
        return len(self.requests) < self.max_requests
    
    def record_request(self):
        """记录请求"""
        self.requests.append(datetime.now().timestamp())
    
    def wait_time(self) -> float:
        """计算需要等待的时间"""
        if self.can_make_request():
            return 0
        
        now = datetime.now().timestamp()
        oldest_request = min(self.requests)
        return self.time_window - (now - oldest_request)


# 常用正则表达式模式
REGEX_PATTERNS = {
    'price': r'[¥￥]\s*(\d+\.?\d*)',
    'sales': r'销量[：:]\s*(\d+)',
    'rating': r'(\d+\.?\d*)\s*分',
    'phone': r'1[3-9]\d{9}',
    'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
    'url': r'https?://[^\s<>"{}|\\^`\[\]]+',
    'chinese': r'[\u4e00-\u9fff]+',
    'number': r'\d+\.?\d*'
}


def get_current_timestamp() -> str:
    """获取当前时间戳字符串"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

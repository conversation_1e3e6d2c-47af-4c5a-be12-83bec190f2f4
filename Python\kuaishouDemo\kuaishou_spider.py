#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手小店商品信息爬虫
作者: AI Assistant
创建时间: 2025-07-16
功能: 获取快手小店的商品信息，包括商品名称、价格、销量、评价等
"""

import requests
import json
import csv
import logging
from typing import Dict, List, Optional
from urllib.parse import urljoin, urlparse
import re
from dataclasses import dataclass
from datetime import datetime
import os
from anti_crawler import AntiCrawlerManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kuaishou_spider.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class ProductInfo:
    """商品信息数据类"""
    product_id: str
    title: str
    price: float
    original_price: Optional[float]
    sales_count: int
    rating: float
    review_count: int
    shop_name: str
    product_url: str
    image_urls: List[str]
    description: str
    category: str
    brand: str
    stock_status: str
    crawl_time: str


class KuaishouSpider:
    """快手小店爬虫类"""
    
    def __init__(self, config_file: str = "config.json"):
        """初始化爬虫"""
        self.config = self.load_config(config_file)
        self.anti_crawler = AntiCrawlerManager(self.config)
        self.session = requests.Session()
        self.setup_session()
        self.products_data = []
        self.consecutive_failures = 0
        
    def load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "headers": {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            },
            "request_delay": [1, 3],  # 请求延时范围（秒）
            "max_retries": 3,
            "timeout": 30,
            "output_format": "csv",  # csv 或 json
            "output_file": "kuaishou_products.csv"
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config
    
    def setup_session(self):
        """设置请求会话"""
        self.session.headers.update(self.config["headers"])
        

        
    def make_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """发送HTTP请求，包含反爬虫处理"""
        try:
            response = self.anti_crawler.make_safe_request(url, **kwargs)
            if response:
                self.consecutive_failures = 0
                return response
            else:
                self.consecutive_failures += 1

                # 检查是否应该停止爬取
                if self.anti_crawler.should_stop_crawling(self.consecutive_failures):
                    logger.error("连续失败次数过多，停止爬取")
                    return None

                return None
        except Exception as e:
            logger.error(f"请求出错: {url}, 错误: {e}")
            self.consecutive_failures += 1
            return None
    
    def parse_product_list(self, html_content: str) -> List[str]:
        """解析商品列表页面，提取商品链接"""
        product_urls = []

        try:
            # 方法1: 从HTML中提取商品链接
            url_patterns = [
                r'href="([^"]*product[^"]*)"',
                r'href="([^"]*goods[^"]*)"',
                r'href="([^"]*item[^"]*)"',
                r'"url":"([^"]*product[^"]*)"',
                r'"link":"([^"]*product[^"]*)"'
            ]

            for pattern in url_patterns:
                matches = re.findall(pattern, html_content)
                for match in matches:
                    if match.startswith('http'):
                        product_urls.append(match)
                    else:
                        # 处理相对链接
                        product_urls.append(urljoin("https://shop.kuaishou.com", match))

            # 方法2: 从JSON数据中提取（如果页面包含JSON数据）
            json_pattern = r'<script[^>]*>.*?window\.__INITIAL_STATE__\s*=\s*({.*?});.*?</script>'
            json_match = re.search(json_pattern, html_content, re.DOTALL)

            if json_match:
                try:
                    json_data = json.loads(json_match.group(1))
                    # 递归搜索JSON中的商品链接
                    self._extract_urls_from_json(json_data, product_urls)
                except json.JSONDecodeError:
                    pass

            # 方法3: 查找商品ID并构建链接
            id_patterns = [
                r'data-product-id="(\d+)"',
                r'product_id["\']:\s*["\']?(\d+)',
                r'goodsId["\']:\s*["\']?(\d+)'
            ]

            for pattern in id_patterns:
                matches = re.findall(pattern, html_content)
                for product_id in matches:
                    product_url = f"https://shop.kuaishou.com/product/{product_id}"
                    product_urls.append(product_url)

        except Exception as e:
            logger.error(f"解析商品列表失败: {e}")

        return list(set(product_urls))  # 去重

    def _extract_urls_from_json(self, data, urls_list):
        """从JSON数据中递归提取URL"""
        if isinstance(data, dict):
            for key, value in data.items():
                if key in ['url', 'link', 'href'] and isinstance(value, str):
                    if 'product' in value or 'goods' in value:
                        urls_list.append(value)
                elif isinstance(value, (dict, list)):
                    self._extract_urls_from_json(value, urls_list)
        elif isinstance(data, list):
            for item in data:
                self._extract_urls_from_json(item, urls_list)
    
    def parse_product_detail(self, html_content: str, product_url: str) -> Optional[ProductInfo]:
        """解析商品详情页面"""
        try:
            # 提取商品ID
            product_id = self._extract_product_id(product_url, html_content)

            # 提取商品标题
            title = self._extract_title(html_content)

            # 提取价格信息
            price, original_price = self._extract_prices(html_content)

            # 提取销量
            sales_count = self._extract_sales_count(html_content)

            # 提取评分和评价数量
            rating, review_count = self._extract_rating_info(html_content)

            # 提取店铺名称
            shop_name = self._extract_shop_name(html_content)

            # 提取图片链接
            image_urls = self._extract_image_urls(html_content)

            # 提取商品描述
            description = self._extract_description(html_content)

            # 提取分类和品牌
            category, brand = self._extract_category_brand(html_content)

            # 提取库存状态
            stock_status = self._extract_stock_status(html_content)

            # 验证必要字段
            if not title or not product_id:
                logger.warning(f"商品信息不完整: {product_url}")
                return None

            return ProductInfo(
                product_id=product_id,
                title=title,
                price=price,
                original_price=original_price,
                sales_count=sales_count,
                rating=rating,
                review_count=review_count,
                shop_name=shop_name,
                product_url=product_url,
                image_urls=image_urls[:5],  # 最多保存5张图片
                description=description,
                category=category,
                brand=brand,
                stock_status=stock_status,
                crawl_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

        except Exception as e:
            logger.error(f"解析商品详情失败: {e}")
            return None

    def _extract_product_id(self, product_url: str, html_content: str) -> str:
        """提取商品ID"""
        # 从URL中提取
        url_patterns = [
            r'product[/_](\d+)',
            r'goods[/_](\d+)',
            r'item[/_](\d+)',
            r'id=(\d+)'
        ]

        for pattern in url_patterns:
            match = re.search(pattern, product_url)
            if match:
                return match.group(1)

        # 从HTML中提取
        html_patterns = [
            r'data-product-id="(\d+)"',
            r'product_id["\']:\s*["\']?(\d+)',
            r'goodsId["\']:\s*["\']?(\d+)',
            r'"id":\s*"?(\d+)"?'
        ]

        for pattern in html_patterns:
            match = re.search(pattern, html_content)
            if match:
                return match.group(1)

        # 生成默认ID
        import hashlib
        return hashlib.md5(product_url.encode()).hexdigest()[:12]

    def _extract_title(self, html_content: str) -> str:
        """提取商品标题"""
        title_patterns = [
            r'<title>([^<]+)</title>',
            r'"title":\s*"([^"]+)"',
            r'<h1[^>]*>([^<]+)</h1>',
            r'class="[^"]*title[^"]*"[^>]*>([^<]+)<',
            r'data-title="([^"]+)"'
        ]

        for pattern in title_patterns:
            match = re.search(pattern, html_content, re.IGNORECASE)
            if match:
                title = match.group(1).strip()
                # 清理标题
                title = re.sub(r'\s+', ' ', title)
                title = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()（）【】""'']', '', title)
                if title and len(title) > 3:
                    return title

        return ""

    def _extract_prices(self, html_content: str) -> tuple:
        """提取价格信息"""
        price = 0.0
        original_price = None

        # 当前价格模式
        price_patterns = [
            r'"price":\s*"?(\d+\.?\d*)"?',
            r'[¥￥]\s*(\d+\.?\d*)',
            r'现价[：:]\s*(\d+\.?\d*)',
            r'售价[：:]\s*(\d+\.?\d*)',
            r'class="[^"]*price[^"]*"[^>]*>[¥￥]?(\d+\.?\d*)'
        ]

        for pattern in price_patterns:
            match = re.search(pattern, html_content)
            if match:
                try:
                    price = float(match.group(1))
                    break
                except:
                    continue

        # 原价模式
        original_price_patterns = [
            r'"original_price":\s*"?(\d+\.?\d*)"?',
            r'原价[：:]\s*[¥￥]?(\d+\.?\d*)',
            r'市场价[：:]\s*[¥￥]?(\d+\.?\d*)',
            r'class="[^"]*original[^"]*"[^>]*>[¥￥]?(\d+\.?\d*)'
        ]

        for pattern in original_price_patterns:
            match = re.search(pattern, html_content)
            if match:
                try:
                    original_price = float(match.group(1))
                    break
                except:
                    continue

        return price, original_price

    def _extract_sales_count(self, html_content: str) -> int:
        """提取销量"""
        sales_patterns = [
            r'"sales":\s*"?(\d+)"?',
            r'销量[：:]\s*(\d+)',
            r'已售[：:]\s*(\d+)',
            r'月销[：:]\s*(\d+)',
            r'(\d+)\s*人付款',
            r'(\d+)\s*笔交易',
            r'成交[：:]\s*(\d+)'
        ]

        for pattern in sales_patterns:
            match = re.search(pattern, html_content)
            if match:
                try:
                    return int(match.group(1))
                except:
                    continue

        return 0

    def _extract_rating_info(self, html_content: str) -> tuple:
        """提取评分和评价数量"""
        rating = 0.0
        review_count = 0

        # 评分模式
        rating_patterns = [
            r'"rating":\s*"?(\d+\.?\d*)"?',
            r'评分[：:]\s*(\d+\.?\d*)',
            r'(\d+\.?\d*)\s*分',
            r'好评率[：:]\s*(\d+\.?\d*)%'
        ]

        for pattern in rating_patterns:
            match = re.search(pattern, html_content)
            if match:
                try:
                    rating = float(match.group(1))
                    if rating > 5:  # 如果是百分比，转换为5分制
                        rating = rating / 20
                    break
                except:
                    continue

        # 评价数量模式
        review_patterns = [
            r'"review_count":\s*"?(\d+)"?',
            r'评价[：:]\s*(\d+)',
            r'评论[：:]\s*(\d+)',
            r'(\d+)\s*条评价',
            r'(\d+)\s*人评价'
        ]

        for pattern in review_patterns:
            match = re.search(pattern, html_content)
            if match:
                try:
                    review_count = int(match.group(1))
                    break
                except:
                    continue

        return rating, review_count

    def _extract_shop_name(self, html_content: str) -> str:
        """提取店铺名称"""
        shop_patterns = [
            r'"shop_name":\s*"([^"]+)"',
            r'店铺[：:]\s*([^<\n]+)',
            r'商家[：:]\s*([^<\n]+)',
            r'class="[^"]*shop[^"]*"[^>]*>([^<]+)<'
        ]

        for pattern in shop_patterns:
            match = re.search(pattern, html_content)
            if match:
                shop_name = match.group(1).strip()
                if shop_name:
                    return shop_name

        return ""

    def _extract_image_urls(self, html_content: str) -> List[str]:
        """提取图片链接"""
        image_urls = []

        # 图片URL模式
        img_patterns = [
            r'src="([^"]*\.(jpg|jpeg|png|webp)[^"]*)"',
            r'data-src="([^"]*\.(jpg|jpeg|png|webp)[^"]*)"',
            r'"image":\s*"([^"]+\.(jpg|jpeg|png|webp))"',
            r'"images":\s*\[([^\]]+)\]'
        ]

        for pattern in img_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if isinstance(match, tuple):
                    url = match[0]
                else:
                    url = match

                # 过滤商品相关图片
                if any(keyword in url.lower() for keyword in ['product', 'goods', 'item', 'upload']):
                    if url.startswith('//'):
                        url = 'https:' + url
                    elif url.startswith('/'):
                        url = 'https://shop.kuaishou.com' + url

                    if url not in image_urls:
                        image_urls.append(url)

        return image_urls

    def _extract_description(self, html_content: str) -> str:
        """提取商品描述"""
        desc_patterns = [
            r'"description":\s*"([^"]+)"',
            r'商品描述[：:]\s*([^<\n]+)',
            r'class="[^"]*desc[^"]*"[^>]*>([^<]+)<',
            r'<meta\s+name="description"\s+content="([^"]+)"'
        ]

        for pattern in desc_patterns:
            match = re.search(pattern, html_content, re.IGNORECASE)
            if match:
                description = match.group(1).strip()
                if description and len(description) > 10:
                    return description

        return ""

    def _extract_category_brand(self, html_content: str) -> tuple:
        """提取分类和品牌"""
        category = ""
        brand = ""

        # 分类模式
        category_patterns = [
            r'"category":\s*"([^"]+)"',
            r'分类[：:]\s*([^<\n]+)',
            r'类别[：:]\s*([^<\n]+)'
        ]

        for pattern in category_patterns:
            match = re.search(pattern, html_content)
            if match:
                category = match.group(1).strip()
                break

        # 品牌模式
        brand_patterns = [
            r'"brand":\s*"([^"]+)"',
            r'品牌[：:]\s*([^<\n]+)',
            r'牌子[：:]\s*([^<\n]+)'
        ]

        for pattern in brand_patterns:
            match = re.search(pattern, html_content)
            if match:
                brand = match.group(1).strip()
                break

        return category, brand

    def _extract_stock_status(self, html_content: str) -> str:
        """提取库存状态"""
        stock_patterns = [
            r'"stock":\s*"([^"]+)"',
            r'库存[：:]\s*([^<\n]+)',
            r'现货|有货|在售' if re.search(r'现货|有货|在售', html_content) else None,
            r'缺货|售罄|下架' if re.search(r'缺货|售罄|下架', html_content) else None
        ]

        for pattern in stock_patterns:
            if pattern:
                if isinstance(pattern, str) and not pattern.startswith('"'):
                    return "有货" if "现货|有货|在售" in pattern else "缺货"
                else:
                    match = re.search(pattern, html_content)
                    if match:
                        return match.group(1).strip()

        return "未知"

    def try_api_request(self, shop_id: str, page: int = 1) -> Optional[Dict]:
        """尝试通过API获取商品列表"""
        api_endpoints = [
            f"https://shop.kuaishou.com/rest/n/shop/product/list?shopId={shop_id}&page={page}",
            f"https://api.kuaishou.com/shop/product/list?shop_id={shop_id}&page={page}",
            f"https://shop.kuaishou.com/api/product/list?shopId={shop_id}&page={page}"
        ]

        headers = self.session.headers.copy()
        headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        })

        for api_url in api_endpoints:
            try:
                response = self.make_request(api_url)
                if response and response.headers.get('content-type', '').startswith('application/json'):
                    return response.json()
            except Exception as e:
                logger.debug(f"API请求失败: {api_url}, 错误: {e}")
                continue

        return None

    def parse_api_response(self, api_data: Dict) -> List[ProductInfo]:
        """解析API响应数据"""
        products = []

        try:
            # 尝试不同的数据结构
            product_list = None

            if 'data' in api_data:
                if 'list' in api_data['data']:
                    product_list = api_data['data']['list']
                elif 'products' in api_data['data']:
                    product_list = api_data['data']['products']
                elif isinstance(api_data['data'], list):
                    product_list = api_data['data']
            elif 'result' in api_data:
                product_list = api_data['result']
            elif isinstance(api_data, list):
                product_list = api_data

            if not product_list:
                return products

            for item in product_list:
                try:
                    product_info = ProductInfo(
                        product_id=str(item.get('id', item.get('productId', ''))),
                        title=item.get('title', item.get('name', '')),
                        price=float(item.get('price', item.get('currentPrice', 0))),
                        original_price=float(item.get('originalPrice', 0)) if item.get('originalPrice') else None,
                        sales_count=int(item.get('sales', item.get('salesCount', 0))),
                        rating=float(item.get('rating', item.get('score', 0))),
                        review_count=int(item.get('reviewCount', item.get('commentCount', 0))),
                        shop_name=item.get('shopName', ''),
                        product_url=item.get('url', f"https://shop.kuaishou.com/product/{item.get('id', '')}"),
                        image_urls=item.get('images', [item.get('image', '')]) if item.get('images') or item.get('image') else [],
                        description=item.get('description', ''),
                        category=item.get('category', ''),
                        brand=item.get('brand', ''),
                        stock_status=item.get('stockStatus', '有货'),
                        crawl_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    )

                    if product_info.title and product_info.product_id:
                        products.append(product_info)

                except Exception as e:
                    logger.warning(f"解析单个商品数据失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"解析API响应失败: {e}")

        return products

    def extract_shop_id(self, shop_url: str) -> Optional[str]:
        """从店铺URL中提取店铺ID"""
        shop_id_patterns = [
            r'profile/(\d+)',
            r'shop/(\d+)',
            r'shopId=(\d+)',
            r'shop_id=(\d+)'
        ]

        for pattern in shop_id_patterns:
            match = re.search(pattern, shop_url)
            if match:
                return match.group(1)

        # 尝试从页面内容中提取
        response = self.make_request(shop_url)
        if response:
            html_patterns = [
                r'"shopId":\s*"?(\d+)"?',
                r'data-shop-id="(\d+)"',
                r'shop_id["\']:\s*["\']?(\d+)'
            ]

            for pattern in html_patterns:
                match = re.search(pattern, response.text)
                if match:
                    return match.group(1)

        return None
    
    def crawl_shop_products(self, shop_url: str, max_pages: int = 5) -> List[ProductInfo]:
        """爬取店铺商品信息"""
        logger.info(f"开始爬取店铺: {shop_url}")

        # 尝试提取店铺ID并使用API
        shop_id = self.extract_shop_id(shop_url)
        if shop_id:
            logger.info(f"找到店铺ID: {shop_id}，尝试使用API获取数据")
            api_products = self.crawl_via_api(shop_id, max_pages)
            if api_products:
                logger.info(f"通过API获取到 {len(api_products)} 个商品")
                return api_products
            else:
                logger.info("API方式失败，切换到HTML解析方式")

        # 使用HTML解析方式
        return self.crawl_via_html(shop_url, max_pages)

    def crawl_via_api(self, shop_id: str, max_pages: int = 5) -> List[ProductInfo]:
        """通过API方式爬取商品"""
        all_products = []

        for page in range(1, max_pages + 1):
            logger.info(f"正在通过API爬取第 {page} 页...")

            api_data = self.try_api_request(shop_id, page)
            if not api_data:
                logger.info("API请求失败，停止爬取")
                break

            products = self.parse_api_response(api_data)
            if not products:
                logger.info("没有找到更多商品，停止爬取")
                break

            all_products.extend(products)
            logger.info(f"第 {page} 页获取到 {len(products)} 个商品")

            # 检查是否还有更多页面
            if len(products) < 20:  # 假设每页最多20个商品
                break

        return all_products

    def crawl_via_html(self, shop_url: str, max_pages: int = 5) -> List[ProductInfo]:
        """通过HTML解析方式爬取商品"""
        all_products = []

        for page in range(1, max_pages + 1):
            logger.info(f"正在通过HTML解析爬取第 {page} 页...")

            # 构建分页URL
            page_url = f"{shop_url}?page={page}"

            response = self.make_request(page_url)
            if not response:
                continue

            # 解析商品列表
            product_urls = self.parse_product_list(response.text)

            if not product_urls:
                logger.info("没有找到更多商品，停止爬取")
                break

            # 爬取每个商品的详情
            page_products = []
            for product_url in product_urls:
                logger.info(f"正在爬取商品: {product_url}")

                product_response = self.make_request(product_url)
                if not product_response:
                    continue

                product_info = self.parse_product_detail(product_response.text, product_url)
                if product_info:
                    page_products.append(product_info)
                    logger.info(f"成功获取商品: {product_info.title}")

            all_products.extend(page_products)
            logger.info(f"第 {page} 页获取到 {len(page_products)} 个商品")

            # 如果这一页商品数量很少，可能已经到最后一页
            if len(page_products) < 5:
                break

        return all_products
    
    def save_to_csv(self, products: List[ProductInfo], filename: str):
        """保存数据到CSV文件"""
        if not products:
            logger.warning("没有数据需要保存")
            return
            
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = [
                'product_id', 'title', 'price', 'original_price', 'sales_count',
                'rating', 'review_count', 'shop_name', 'product_url', 'image_urls',
                'description', 'category', 'brand', 'stock_status', 'crawl_time'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for product in products:
                writer.writerow({
                    'product_id': product.product_id,
                    'title': product.title,
                    'price': product.price,
                    'original_price': product.original_price,
                    'sales_count': product.sales_count,
                    'rating': product.rating,
                    'review_count': product.review_count,
                    'shop_name': product.shop_name,
                    'product_url': product.product_url,
                    'image_urls': '|'.join(product.image_urls),
                    'description': product.description,
                    'category': product.category,
                    'brand': product.brand,
                    'stock_status': product.stock_status,
                    'crawl_time': product.crawl_time
                })
        
        logger.info(f"数据已保存到: {filename}")
    
    def save_to_json(self, products: List[ProductInfo], filename: str):
        """保存数据到JSON文件"""
        if not products:
            logger.warning("没有数据需要保存")
            return
            
        data = []
        for product in products:
            data.append({
                'product_id': product.product_id,
                'title': product.title,
                'price': product.price,
                'original_price': product.original_price,
                'sales_count': product.sales_count,
                'rating': product.rating,
                'review_count': product.review_count,
                'shop_name': product.shop_name,
                'product_url': product.product_url,
                'image_urls': product.image_urls,
                'description': product.description,
                'category': product.category,
                'brand': product.brand,
                'stock_status': product.stock_status,
                'crawl_time': product.crawl_time
            })
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到: {filename}")

    def save_to_excel(self, products: List[ProductInfo], filename: str):
        """保存数据到Excel文件"""
        if not products:
            logger.warning("没有数据需要保存")
            return

        try:
            import pandas as pd

            data = []
            for product in products:
                data.append({
                    '商品ID': product.product_id,
                    '商品标题': product.title,
                    '当前价格': product.price,
                    '原价': product.original_price,
                    '销量': product.sales_count,
                    '评分': product.rating,
                    '评价数': product.review_count,
                    '店铺名称': product.shop_name,
                    '商品链接': product.product_url,
                    '图片链接': '|'.join(product.image_urls),
                    '商品描述': product.description,
                    '分类': product.category,
                    '品牌': product.brand,
                    '库存状态': product.stock_status,
                    '爬取时间': product.crawl_time
                })

            df = pd.DataFrame(data)
            df.to_excel(filename, index=False, engine='openpyxl')
            logger.info(f"数据已保存到Excel文件: {filename}")

        except ImportError:
            logger.error("需要安装pandas和openpyxl库才能保存Excel文件")
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")

    def clean_and_validate_data(self, products: List[ProductInfo]) -> List[ProductInfo]:
        """清洗和验证数据"""
        cleaned_products = []

        for product in products:
            # 数据清洗
            product.title = self._clean_text(product.title)
            product.description = self._clean_text(product.description)
            product.shop_name = self._clean_text(product.shop_name)
            product.category = self._clean_text(product.category)
            product.brand = self._clean_text(product.brand)

            # 价格验证和修正
            if product.price < 0:
                product.price = 0.0
            if product.original_price and product.original_price < product.price:
                product.original_price = None

            # 销量和评价数验证
            if product.sales_count < 0:
                product.sales_count = 0
            if product.review_count < 0:
                product.review_count = 0

            # 评分验证
            if product.rating < 0 or product.rating > 5:
                product.rating = 0.0

            # URL验证
            if not product.product_url.startswith('http'):
                continue

            # 必要字段验证
            if not product.title or not product.product_id:
                continue

            cleaned_products.append(product)

        logger.info(f"数据清洗完成，有效商品数: {len(cleaned_products)}")
        return cleaned_products

    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)

        # 移除特殊字符（保留中文、英文、数字和常用标点）
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()（）【】""''¥￥%]', '', text)

        return text.strip()

    def run(self, shop_url: str, max_pages: int = 5):
        """运行爬虫"""
        try:
            logger.info("快手小店爬虫开始运行...")

            # 爬取商品信息
            products = self.crawl_shop_products(shop_url, max_pages)

            if not products:
                logger.warning("没有获取到任何商品信息")
                return

            logger.info(f"原始数据获取到 {len(products)} 个商品信息")

            # 数据清洗和验证
            products = self.clean_and_validate_data(products)

            if not products:
                logger.warning("数据清洗后没有有效的商品信息")
                return

            logger.info(f"清洗后有效商品数: {len(products)}")

            # 保存数据到不同格式
            output_format = self.config["output_format"].lower()
            output_file = self.config["output_file"]

            if output_format == "json":
                self.save_to_json(products, output_file.replace('.csv', '.json'))
            elif output_format == "excel":
                self.save_to_excel(products, output_file.replace('.csv', '.xlsx'))
            else:
                self.save_to_csv(products, output_file)

            # 生成摘要报告
            self.save_summary_report(products)

            logger.info("爬虫运行完成!")

        except Exception as e:
            logger.error(f"爬虫运行出错: {e}")

    def generate_summary_report(self, products: List[ProductInfo]) -> Dict:
        """生成数据摘要报告"""
        if not products:
            return {}

        total_products = len(products)
        total_sales = sum(p.sales_count for p in products)
        avg_price = sum(p.price for p in products) / total_products
        avg_rating = sum(p.rating for p in products if p.rating > 0) / len([p for p in products if p.rating > 0]) if any(p.rating > 0 for p in products) else 0

        # 价格分布
        price_ranges = {
            '0-50': len([p for p in products if 0 <= p.price <= 50]),
            '51-100': len([p for p in products if 51 <= p.price <= 100]),
            '101-200': len([p for p in products if 101 <= p.price <= 200]),
            '201-500': len([p for p in products if 201 <= p.price <= 500]),
            '500+': len([p for p in products if p.price > 500])
        }

        # 销量分布
        sales_ranges = {
            '0-10': len([p for p in products if 0 <= p.sales_count <= 10]),
            '11-50': len([p for p in products if 11 <= p.sales_count <= 50]),
            '51-100': len([p for p in products if 51 <= p.sales_count <= 100]),
            '101-500': len([p for p in products if 101 <= p.sales_count <= 500]),
            '500+': len([p for p in products if p.sales_count > 500])
        }

        # 店铺统计
        shop_stats = {}
        for product in products:
            if product.shop_name:
                shop_stats[product.shop_name] = shop_stats.get(product.shop_name, 0) + 1

        report = {
            'total_products': total_products,
            'total_sales': total_sales,
            'average_price': round(avg_price, 2),
            'average_rating': round(avg_rating, 2),
            'price_distribution': price_ranges,
            'sales_distribution': sales_ranges,
            'top_shops': dict(sorted(shop_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
            'crawl_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return report

    def save_summary_report(self, products: List[ProductInfo], filename: str = "summary_report.json"):
        """保存摘要报告"""
        report = self.generate_summary_report(products)

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"摘要报告已保存到: {filename}")
        return report

    def handle_crawl_error(self, error: Exception, context: str):
        """处理爬取错误"""
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        logger.error(f"爬取错误 - {context}: {error}")

        # 保存错误日志到文件
        try:
            error_log_file = "error_log.json"
            if os.path.exists(error_log_file):
                with open(error_log_file, 'r', encoding='utf-8') as f:
                    error_logs = json.load(f)
            else:
                error_logs = []

            error_logs.append(error_info)

            # 只保留最近100条错误记录
            if len(error_logs) > 100:
                error_logs = error_logs[-100:]

            with open(error_log_file, 'w', encoding='utf-8') as f:
                json.dump(error_logs, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"保存错误日志失败: {e}")

    def get_crawl_statistics(self) -> Dict:
        """获取爬取统计信息"""
        stats = self.anti_crawler.log_request_stats()
        stats.update({
            'consecutive_failures': self.consecutive_failures,
            'products_crawled': len(self.products_data),
            'crawl_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        return stats


if __name__ == "__main__":
    # 使用示例
    spider = KuaishouSpider()
    
    # 请替换为实际的快手小店URL
    shop_url = "https://shop.kuaishou.com/profile/123456789"
    
    # 运行爬虫
    spider.run(shop_url, max_pages=3)

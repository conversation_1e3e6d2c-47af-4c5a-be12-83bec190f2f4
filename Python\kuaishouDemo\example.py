#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手小店爬虫使用示例
"""

from kuaishou_spider import KuaishouSpider
import json
import time

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建爬虫实例
    spider = KuaishouSpider()
    
    # 设置要爬取的店铺URL（请替换为实际的URL）
    shop_url = "https://shop.kuaishou.com/profile/123456789"
    
    print(f"开始爬取店铺: {shop_url}")
    
    # 开始爬取（爬取前2页作为示例）
    spider.run(shop_url, max_pages=2)
    
    print("基本爬取完成！")

def example_custom_config():
    """自定义配置示例"""
    print("\n=== 自定义配置示例 ===")
    
    # 创建爬虫实例
    spider = KuaishouSpider()
    
    # 修改配置
    spider.config["output_format"] = "json"
    spider.config["output_file"] = "custom_products.json"
    spider.config["request_delay"] = [3, 6]  # 增加延时
    spider.config["max_retries"] = 5
    
    shop_url = "https://shop.kuaishou.com/profile/123456789"
    
    print(f"使用自定义配置爬取: {shop_url}")
    print(f"输出格式: {spider.config['output_format']}")
    print(f"输出文件: {spider.config['output_file']}")
    
    spider.run(shop_url, max_pages=2)
    
    print("自定义配置爬取完成！")

def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    spider = KuaishouSpider()
    shop_url = "https://shop.kuaishou.com/profile/123456789"
    
    # 爬取数据
    products = spider.crawl_shop_products(shop_url, max_pages=2)
    
    if not products:
        print("没有获取到商品数据")
        return
    
    # 数据清洗
    cleaned_products = spider.clean_and_validate_data(products)
    
    print(f"原始商品数: {len(products)}")
    print(f"清洗后商品数: {len(cleaned_products)}")
    
    # 生成摘要报告
    report = spider.generate_summary_report(cleaned_products)
    
    print("\n商品数据摘要:")
    print(f"  总商品数: {report['total_products']}")
    print(f"  总销量: {report['total_sales']}")
    print(f"  平均价格: ¥{report['average_price']}")
    print(f"  平均评分: {report['average_rating']}")
    
    print("\n价格分布:")
    for price_range, count in report['price_distribution'].items():
        print(f"  {price_range}元: {count}个商品")
    
    print("\n销量分布:")
    for sales_range, count in report['sales_distribution'].items():
        print(f"  {sales_range}件: {count}个商品")
    
    # 保存报告
    spider.save_summary_report(cleaned_products, "analysis_report.json")
    print("\n分析报告已保存到: analysis_report.json")

def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    spider = KuaishouSpider()
    
    # 使用无效URL测试错误处理
    invalid_url = "https://invalid-url.com"
    
    try:
        spider.run(invalid_url, max_pages=1)
    except Exception as e:
        print(f"捕获到错误: {e}")
        
        # 获取统计信息
        stats = spider.get_crawl_statistics()
        print(f"连续失败次数: {stats['consecutive_failures']}")
    
    print("错误处理示例完成")

def example_batch_crawling():
    """批量爬取示例"""
    print("\n=== 批量爬取示例 ===")
    
    # 多个店铺URL列表（请替换为实际的URL）
    shop_urls = [
        "https://shop.kuaishou.com/profile/123456789",
        "https://shop.kuaishou.com/profile/987654321",
        "https://shop.kuaishou.com/profile/555666777"
    ]
    
    spider = KuaishouSpider()
    all_products = []
    
    for i, shop_url in enumerate(shop_urls, 1):
        print(f"\n正在爬取第 {i}/{len(shop_urls)} 个店铺: {shop_url}")
        
        try:
            products = spider.crawl_shop_products(shop_url, max_pages=2)
            if products:
                all_products.extend(products)
                print(f"获取到 {len(products)} 个商品")
            else:
                print("未获取到商品数据")
        except Exception as e:
            print(f"爬取失败: {e}")
            continue
        
        # 添加延时避免请求过快
        time.sleep(5)
    
    if all_products:
        # 清洗数据
        cleaned_products = spider.clean_and_validate_data(all_products)
        
        # 保存所有数据
        spider.save_to_csv(cleaned_products, "batch_products.csv")
        spider.save_summary_report(cleaned_products, "batch_report.json")
        
        print(f"\n批量爬取完成！总共获取 {len(cleaned_products)} 个有效商品")
    else:
        print("\n批量爬取未获取到任何数据")

def example_export_formats():
    """不同导出格式示例"""
    print("\n=== 导出格式示例 ===")
    
    spider = KuaishouSpider()
    shop_url = "https://shop.kuaishou.com/profile/123456789"
    
    # 爬取数据
    products = spider.crawl_shop_products(shop_url, max_pages=1)
    
    if not products:
        print("没有获取到商品数据")
        return
    
    cleaned_products = spider.clean_and_validate_data(products)
    
    # 导出为不同格式
    print("导出为CSV格式...")
    spider.save_to_csv(cleaned_products, "products.csv")
    
    print("导出为JSON格式...")
    spider.save_to_json(cleaned_products, "products.json")
    
    print("导出为Excel格式...")
    spider.save_to_excel(cleaned_products, "products.xlsx")
    
    print("所有格式导出完成！")

def main():
    """主函数"""
    print("快手小店爬虫使用示例")
    print("=" * 50)
    
    # 注意：以下示例使用的是示例URL，实际使用时请替换为真实的快手小店URL
    print("注意: 示例中的URL为演示用途，请替换为实际的快手小店URL")
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_custom_config()
        example_data_analysis()
        example_error_handling()
        example_batch_crawling()
        example_export_formats()
        
    except KeyboardInterrupt:
        print("\n\n用户中断示例运行")
    except Exception as e:
        print(f"\n\n示例运行出错: {e}")
    
    print("\n" + "=" * 50)
    print("所有示例运行完成！")
    print("请查看生成的文件:")
    print("- kuaishou_products.csv")
    print("- custom_products.json") 
    print("- analysis_report.json")
    print("- batch_products.csv")
    print("- batch_report.json")
    print("- products.csv/json/xlsx")
    print("- summary_report.json")
    print("- error_log.json")
    print("- kuaishou_spider.log")

if __name__ == "__main__":
    main()

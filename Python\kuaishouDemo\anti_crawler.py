#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反爬虫处理模块
包含各种反爬虫技术和错误处理机制
"""

import random
import time
import logging
import requests
from typing import Dict, List, Optional, Callable
from fake_useragent import UserAgent
import json
from datetime import datetime, timedelta
from urllib.parse import urlparse
import hashlib

logger = logging.getLogger(__name__)


class UserAgentRotator:
    """User-Agent轮换器"""
    
    def __init__(self):
        try:
            self.ua = UserAgent()
            self.custom_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
        except:
            self.ua = None
            self.custom_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
    
    def get_random_ua(self) -> str:
        """获取随机User-Agent"""
        try:
            if self.ua and random.choice([True, False]):
                return self.ua.random
            else:
                return random.choice(self.custom_agents)
        except:
            return self.custom_agents[0]


class ProxyRotator:
    """代理轮换器"""
    
    def __init__(self, proxy_list: List[str] = None):
        self.proxy_list = proxy_list or []
        self.current_index = 0
        self.failed_proxies = set()
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """获取可用代理"""
        if not self.proxy_list:
            return None
        
        available_proxies = [p for p in self.proxy_list if p not in self.failed_proxies]
        if not available_proxies:
            # 重置失败代理列表
            self.failed_proxies.clear()
            available_proxies = self.proxy_list
        
        proxy = random.choice(available_proxies)
        return {
            'http': proxy,
            'https': proxy
        }
    
    def mark_proxy_failed(self, proxy: str):
        """标记代理失败"""
        self.failed_proxies.add(proxy)


class RateLimiter:
    """请求频率限制器"""
    
    def __init__(self, max_requests: int = 10, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        self.domain_requests = {}
    
    def can_make_request(self, domain: str = None) -> bool:
        """检查是否可以发起请求"""
        now = datetime.now()
        
        # 全局频率限制
        self.requests = [req_time for req_time in self.requests 
                        if (now - req_time).seconds < self.time_window]
        
        if len(self.requests) >= self.max_requests:
            return False
        
        # 域名级别频率限制
        if domain:
            if domain not in self.domain_requests:
                self.domain_requests[domain] = []
            
            self.domain_requests[domain] = [req_time for req_time in self.domain_requests[domain]
                                          if (now - req_time).seconds < self.time_window]
            
            if len(self.domain_requests[domain]) >= self.max_requests // 2:
                return False
        
        return True
    
    def record_request(self, domain: str = None):
        """记录请求"""
        now = datetime.now()
        self.requests.append(now)
        
        if domain:
            if domain not in self.domain_requests:
                self.domain_requests[domain] = []
            self.domain_requests[domain].append(now)
    
    def get_wait_time(self, domain: str = None) -> float:
        """获取需要等待的时间"""
        if self.can_make_request(domain):
            return 0
        
        now = datetime.now()
        if self.requests:
            oldest_request = min(self.requests)
            return max(0, self.time_window - (now - oldest_request).seconds)
        
        return self.time_window


class RetryHandler:
    """重试处理器"""
    
    def __init__(self, max_retries: int = 3, backoff_factor: float = 1.0):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
    
    def retry_with_backoff(self, func: Callable, *args, **kwargs):
        """带退避策略的重试"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    wait_time = self.backoff_factor * (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"请求失败，{wait_time:.2f}秒后重试 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")
                    time.sleep(wait_time)
                else:
                    logger.error(f"重试次数已用完，最终失败: {e}")
        
        raise last_exception


class CaptchaDetector:
    """验证码检测器"""
    
    def __init__(self):
        self.captcha_keywords = [
            '验证码', 'captcha', '人机验证', '滑动验证',
            'verification', 'challenge', '安全验证',
            'robot', 'bot', 'automated'
        ]
        
        self.blocked_status_codes = [403, 429, 503, 521, 522, 523, 524]
    
    def is_captcha_page(self, response: requests.Response) -> bool:
        """检测是否为验证码页面"""
        # 检查状态码
        if response.status_code in self.blocked_status_codes:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        for keyword in self.captcha_keywords:
            if keyword in content:
                return True
        
        # 检查响应头
        headers = response.headers
        if 'cf-ray' in headers and response.status_code == 403:
            return True
        
        return False
    
    def handle_captcha(self, response: requests.Response) -> bool:
        """处理验证码"""
        logger.warning("检测到验证码或反爬虫机制")
        
        # 这里可以集成验证码识别服务
        # 例如：2captcha, anti-captcha等
        
        # 暂时返回False，表示无法处理
        return False


class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.sessions = {}
        self.session_cookies = {}
        self.ua_rotator = UserAgentRotator()
        self.rate_limiter = RateLimiter()
        self.retry_handler = RetryHandler()
        self.captcha_detector = CaptchaDetector()
    
    def get_session(self, domain: str) -> requests.Session:
        """获取或创建会话"""
        if domain not in self.sessions:
            session = requests.Session()
            
            # 设置随机User-Agent
            session.headers.update({
                'User-Agent': self.ua_rotator.get_random_ua(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1'
            })
            
            self.sessions[domain] = session
        
        return self.sessions[domain]
    
    def make_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """发起请求"""
        domain = urlparse(url).netloc
        
        # 频率限制检查
        if not self.rate_limiter.can_make_request(domain):
            wait_time = self.rate_limiter.get_wait_time(domain)
            logger.info(f"频率限制，等待 {wait_time:.2f} 秒")
            time.sleep(wait_time)
        
        session = self.get_session(domain)
        
        def _make_request():
            # 随机延时
            time.sleep(random.uniform(1, 3))
            
            # 更新User-Agent
            session.headers['User-Agent'] = self.ua_rotator.get_random_ua()
            
            response = session.get(url, **kwargs)
            
            # 检查验证码
            if self.captcha_detector.is_captcha_page(response):
                if not self.captcha_detector.handle_captcha(response):
                    raise Exception("遇到验证码，无法继续")
            
            response.raise_for_status()
            return response
        
        try:
            response = self.retry_handler.retry_with_backoff(_make_request)
            self.rate_limiter.record_request(domain)
            return response
        except Exception as e:
            logger.error(f"请求失败: {url}, 错误: {e}")
            return None


class AntiCrawlerManager:
    """反爬虫管理器"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.session_manager = SessionManager()
        self.proxy_rotator = ProxyRotator(self.config.get('proxies', []))
        
        # 配置参数
        self.max_retries = self.config.get('max_retries', 3)
        self.request_delay = self.config.get('request_delay', [1, 3])
        self.enable_proxy = self.config.get('enable_proxy', False)
        
    def make_safe_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """安全请求方法"""
        # 添加代理支持
        if self.enable_proxy:
            proxy = self.proxy_rotator.get_proxy()
            if proxy:
                kwargs['proxies'] = proxy
        
        # 设置超时
        kwargs.setdefault('timeout', 30)
        
        return self.session_manager.make_request(url, **kwargs)
    
    def get_random_delay(self) -> float:
        """获取随机延时"""
        return random.uniform(self.request_delay[0], self.request_delay[1])
    
    def should_stop_crawling(self, consecutive_failures: int) -> bool:
        """判断是否应该停止爬取"""
        max_consecutive_failures = self.config.get('max_consecutive_failures', 10)
        return consecutive_failures >= max_consecutive_failures
    
    def log_request_stats(self):
        """记录请求统计信息"""
        stats = {
            'total_requests': len(self.session_manager.rate_limiter.requests),
            'failed_proxies': len(self.proxy_rotator.failed_proxies),
            'active_sessions': len(self.session_manager.sessions)
        }
        
        logger.info(f"请求统计: {stats}")
        return stats

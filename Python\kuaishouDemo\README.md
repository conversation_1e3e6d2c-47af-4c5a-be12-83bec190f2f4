# 快手小店商品信息爬虫

一个功能完整的快手小店商品信息爬虫，支持多种数据格式导出、反爬虫处理、错误重试等功能。

## 功能特性

- 🚀 **多种爬取方式**: 支持API接口和HTML解析两种方式
- 🛡️ **反爬虫处理**: 集成User-Agent轮换、请求频率限制、代理支持等
- 📊 **多格式导出**: 支持CSV、JSON、Excel格式数据导出
- 🔄 **智能重试**: 带退避策略的请求重试机制
- 📈 **数据统计**: 自动生成爬取数据的摘要报告
- 🧹 **数据清洗**: 自动清洗和验证爬取的数据
- 📝 **详细日志**: 完整的日志记录和错误追踪

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 基本使用

```python
from kuaishou_spider import KuaishouSpider

# 创建爬虫实例
spider = KuaishouSpider()

# 设置要爬取的店铺URL
shop_url = "https://shop.kuaishou.com/profile/123456789"

# 开始爬取（爬取前3页）
spider.run(shop_url, max_pages=3)
```

### 自定义配置

```python
from kuaishou_spider import KuaishouSpider

# 使用自定义配置文件
spider = KuaishouSpider("my_config.json")

# 或者直接修改配置
spider.config["output_format"] = "excel"
spider.config["max_retries"] = 5
spider.config["request_delay"] = [2, 5]

spider.run(shop_url, max_pages=5)
```

## 配置说明

配置文件 `config.json` 包含以下选项：

### 基本配置

```json
{
  "output_format": "csv",           // 输出格式: csv, json, excel
  "output_file": "kuaishou_products.csv",  // 输出文件名
  "max_retries": 3,                 // 最大重试次数
  "timeout": 30,                    // 请求超时时间
  "request_delay": [2, 5]           // 请求延时范围（秒）
}
```

### 反爬虫配置

```json
{
  "headers": {
    "User-Agent": "Mozilla/5.0 ...",
    "Accept": "text/html,application/xhtml+xml...",
    // 更多请求头...
  },
  "proxy": {
    "enabled": false,
    "http": "http://127.0.0.1:8080",
    "https": "https://127.0.0.1:8080"
  },
  "max_consecutive_failures": 10    // 最大连续失败次数
}
```

### 爬取设置

```json
{
  "crawl_settings": {
    "max_pages_per_shop": 10,       // 每个店铺最大爬取页数
    "max_products_per_page": 20,    // 每页最大商品数
    "enable_image_download": false,  // 是否下载商品图片
    "image_download_path": "./images/",
    "enable_description_crawl": true,
    "enable_review_crawl": false,
    "max_reviews_per_product": 50
  }
}
```

## 输出数据格式

爬虫会提取以下商品信息：

| 字段名 | 描述 | 示例 |
|--------|------|------|
| product_id | 商品ID | "123456789" |
| title | 商品标题 | "iPhone 15 Pro Max" |
| price | 当前价格 | 8999.00 |
| original_price | 原价 | 9999.00 |
| sales_count | 销量 | 1234 |
| rating | 评分 | 4.8 |
| review_count | 评价数 | 567 |
| shop_name | 店铺名称 | "Apple官方旗舰店" |
| product_url | 商品链接 | "https://shop.kuaishou.com/product/123456789" |
| image_urls | 图片链接 | "url1\|url2\|url3" |
| description | 商品描述 | "全新iPhone..." |
| category | 分类 | "手机数码" |
| brand | 品牌 | "Apple" |
| stock_status | 库存状态 | "有货" |
| crawl_time | 爬取时间 | "2025-07-16 10:30:00" |

## 高级功能

### 1. 数据清洗和验证

```python
# 爬虫会自动进行数据清洗
products = spider.crawl_shop_products(shop_url)
cleaned_products = spider.clean_and_validate_data(products)
```

### 2. 生成摘要报告

```python
# 自动生成数据摘要报告
report = spider.generate_summary_report(products)
spider.save_summary_report(products, "report.json")
```

### 3. 错误处理和监控

```python
# 获取爬取统计信息
stats = spider.get_crawl_statistics()
print(f"已爬取商品数: {stats['products_crawled']}")
print(f"连续失败次数: {stats['consecutive_failures']}")
```

### 4. 使用代理

```python
# 在配置文件中启用代理
{
  "proxy": {
    "enabled": true,
    "http": "http://proxy.example.com:8080",
    "https": "https://proxy.example.com:8080"
  },
  "proxies": [
    "http://proxy1.example.com:8080",
    "http://proxy2.example.com:8080"
  ]
}
```

## 命令行使用

创建一个命令行脚本 `run_spider.py`：

```python
#!/usr/bin/env python3
import argparse
from kuaishou_spider import KuaishouSpider

def main():
    parser = argparse.ArgumentParser(description='快手小店商品爬虫')
    parser.add_argument('shop_url', help='店铺URL')
    parser.add_argument('--pages', type=int, default=5, help='爬取页数')
    parser.add_argument('--config', default='config.json', help='配置文件')
    parser.add_argument('--format', choices=['csv', 'json', 'excel'], 
                       default='csv', help='输出格式')
    
    args = parser.parse_args()
    
    spider = KuaishouSpider(args.config)
    spider.config['output_format'] = args.format
    
    spider.run(args.shop_url, max_pages=args.pages)

if __name__ == '__main__':
    main()
```

使用方法：

```bash
python run_spider.py "https://shop.kuaishou.com/profile/123456789" --pages 3 --format excel
```

## 注意事项

1. **遵守robots.txt**: 请确保遵守目标网站的robots.txt规则
2. **合理使用**: 请合理控制爬取频率，避免对服务器造成过大压力
3. **法律合规**: 请确保爬取行为符合相关法律法规
4. **数据使用**: 爬取的数据仅供学习和研究使用

## 常见问题

### Q: 遇到验证码怎么办？
A: 爬虫会自动检测验证码页面，建议降低爬取频率或使用代理。

### Q: 如何提高爬取成功率？
A: 可以通过以下方式：
- 增加请求延时
- 使用代理IP
- 更换User-Agent
- 减少并发请求

### Q: 数据不完整怎么办？
A: 快手小店可能使用动态加载，建议：
- 检查网络连接
- 更新解析规则
- 使用浏览器开发者工具分析页面结构

## 更新日志

### v1.0.0 (2025-07-16)
- 初始版本发布
- 支持基本的商品信息爬取
- 集成反爬虫处理机制
- 支持多种数据格式导出

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至: <EMAIL>

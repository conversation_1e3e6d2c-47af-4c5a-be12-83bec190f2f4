#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手小店爬虫命令行工具
"""

import argparse
import sys
import os
import json
from kuaishou_spider import KuaishouSpider
import logging

def setup_logging(verbose: bool = False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('spider.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def validate_url(url: str) -> bool:
    """验证URL格式"""
    if not url.startswith('http'):
        return False
    if 'kuaishou.com' not in url:
        return False
    return True

def create_default_config():
    """创建默认配置文件"""
    default_config = {
        "headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        },
        "request_delay": [2, 5],
        "max_retries": 3,
        "timeout": 30,
        "output_format": "csv",
        "output_file": "kuaishou_products.csv",
        "max_consecutive_failures": 10,
        "enable_proxy": False,
        "proxies": []
    }
    
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    print("已创建默认配置文件: config.json")

def main():
    parser = argparse.ArgumentParser(
        description='快手小店商品信息爬虫',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s "https://shop.kuaishou.com/profile/123456789"
  %(prog)s "https://shop.kuaishou.com/profile/123456789" --pages 5 --format excel
  %(prog)s --create-config  # 创建默认配置文件
        """
    )
    
    parser.add_argument('shop_url', nargs='?', help='店铺URL')
    parser.add_argument('--pages', type=int, default=5, help='爬取页数 (默认: 5)')
    parser.add_argument('--config', default='config.json', help='配置文件路径 (默认: config.json)')
    parser.add_argument('--format', choices=['csv', 'json', 'excel'], 
                       help='输出格式 (覆盖配置文件设置)')
    parser.add_argument('--output', help='输出文件名 (覆盖配置文件设置)')
    parser.add_argument('--delay', type=float, nargs=2, metavar=('MIN', 'MAX'),
                       help='请求延时范围，例如: --delay 1 3')
    parser.add_argument('--retries', type=int, help='最大重试次数')
    parser.add_argument('--timeout', type=int, help='请求超时时间（秒）')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--create-config', action='store_true', help='创建默认配置文件')
    parser.add_argument('--proxy', help='代理服务器，格式: http://proxy:port')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 创建配置文件
    if args.create_config:
        create_default_config()
        return
    
    # 检查必需参数
    if not args.shop_url:
        parser.error("需要提供店铺URL")
    
    # 验证URL
    if not validate_url(args.shop_url):
        print("错误: 无效的快手小店URL")
        sys.exit(1)
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"配置文件不存在: {args.config}")
        print("使用 --create-config 创建默认配置文件")
        sys.exit(1)
    
    try:
        # 创建爬虫实例
        spider = KuaishouSpider(args.config)
        
        # 应用命令行参数覆盖配置
        if args.format:
            spider.config['output_format'] = args.format
        
        if args.output:
            spider.config['output_file'] = args.output
        
        if args.delay:
            spider.config['request_delay'] = args.delay
        
        if args.retries:
            spider.config['max_retries'] = args.retries
        
        if args.timeout:
            spider.config['timeout'] = args.timeout
        
        if args.proxy:
            spider.config['enable_proxy'] = True
            spider.config['proxies'] = [args.proxy]
        
        # 显示配置信息
        print(f"店铺URL: {args.shop_url}")
        print(f"爬取页数: {args.pages}")
        print(f"输出格式: {spider.config['output_format']}")
        print(f"输出文件: {spider.config['output_file']}")
        print(f"请求延时: {spider.config['request_delay']} 秒")
        print(f"最大重试: {spider.config['max_retries']} 次")
        print("-" * 50)
        
        # 开始爬取
        spider.run(args.shop_url, max_pages=args.pages)
        
        # 显示统计信息
        stats = spider.get_crawl_statistics()
        print("\n" + "=" * 50)
        print("爬取统计:")
        print(f"  已爬取商品数: {stats.get('products_crawled', 0)}")
        print(f"  总请求次数: {stats.get('total_requests', 0)}")
        print(f"  连续失败次数: {stats.get('consecutive_failures', 0)}")
        print(f"  活跃会话数: {stats.get('active_sessions', 0)}")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n用户中断爬取")
        sys.exit(0)
    except Exception as e:
        print(f"爬取过程中出现错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实API接口的快手小店爬虫
根据抓包结果分析的API接口进行数据获取
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import re
from urllib.parse import urlencode, parse_qs, urlparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KuaishouAPISpider:
    """基于API的快手小店爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://app.kwaixiaodian.com"
        self.setup_headers()
        
    def setup_headers(self):
        """设置请求头"""
        self.session.headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-<PERSON><PERSON>,zh-Hans;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Kwai/13.6.20.9324 MerchantWeb/1.0 ISLP/0 StatusHT/62 KDT/PHONE iosSCH/0 TitleHT/44 NetType/WIFI ISDM/0 ICFO/0 locale/zh-Hans CT/0 Yoda/3.3.6 ISLB/0 CoIS/2 ISLM/0 WebViewType/WK BHT/102 AZPREFIX/az3',
            'kpf': 'IPHONE_H5',
            'kpn': 'KUAISHOU',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty'
        })
    
    def extract_api_endpoints(self, postman_data: Dict) -> List[Dict]:
        """从Postman数据中提取API端点"""
        endpoints = []
        
        if 'item' in postman_data:
            for item in postman_data['item']:
                request_info = item.get('request', {})
                url_info = request_info.get('url', {})
                
                endpoint = {
                    'name': item.get('name', ''),
                    'method': request_info.get('method', 'GET'),
                    'path': url_info.get('path', ''),
                    'query': url_info.get('query', []),
                    'headers': request_info.get('header', []),
                    'body': request_info.get('body', {})
                }
                endpoints.append(endpoint)
        
        return endpoints
    
    def find_product_related_apis(self, endpoints: List[Dict]) -> List[Dict]:
        """查找与商品相关的API"""
        product_apis = []
        
        # 关键词匹配
        product_keywords = [
            'product', 'goods', 'item', 'selection', 'shop', 'store',
            'list', 'search', 'query', 'info', 'detail'
        ]
        
        for endpoint in endpoints:
            name = endpoint['name'].lower()
            path = endpoint['path'].lower() if isinstance(endpoint['path'], str) else str(endpoint['path']).lower()
            
            # 检查是否包含商品相关关键词
            is_product_related = any(keyword in name or keyword in path for keyword in product_keywords)
            
            if is_product_related:
                product_apis.append(endpoint)
                logger.info(f"发现可能的商品API: {endpoint['name']}")
        
        return product_apis
    
    def analyze_selection_decision_api(self) -> Dict:
        """分析选品决策API"""
        # 基于抓包结果的选品决策API
        api_url = f"{self.base_url}/gateway/distribute/match/selection/decision/app/info"
        
        payload = {
            "relItemId": "24423416370241",  # 这个需要根据实际情况调整
            "activityKeyword": "",
            "sceneCode": "selection_decision",
            "themeSceneId": "",
            "relSceneCode": "CPS",
            "entranceScene": ""
        }
        
        try:
            response = self.session.post(api_url, json=payload)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"选品决策API请求失败: {response.status_code}")
                return {}
        except Exception as e:
            logger.error(f"选品决策API请求异常: {e}")
            return {}
    
    def get_user_pid_bind_list(self) -> Dict:
        """获取用户PID绑定列表"""
        api_url = f"{self.base_url}/distribute/wireless/selection/home/<USER>"
        
        try:
            response = self.session.get(api_url)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"PID绑定列表API请求失败: {response.status_code}")
                return {}
        except Exception as e:
            logger.error(f"PID绑定列表API请求异常: {e}")
            return {}
    
    def search_products_by_keyword(self, keyword: str, page: int = 1, page_size: int = 20) -> Dict:
        """根据关键词搜索商品（推测的API）"""
        # 这是一个推测的商品搜索API，需要根据实际情况调整
        possible_search_apis = [
            "/gateway/distribute/search/products",
            "/distribute/wireless/selection/search",
            "/gateway/shop/product/search",
            "/api/product/list"
        ]
        
        for api_path in possible_search_apis:
            api_url = f"{self.base_url}{api_path}"
            
            # 尝试不同的参数格式
            params_variations = [
                {"keyword": keyword, "page": page, "pageSize": page_size},
                {"q": keyword, "offset": (page-1)*page_size, "limit": page_size},
                {"search": keyword, "pageNum": page, "pageSize": page_size}
            ]
            
            for params in params_variations:
                try:
                    response = self.session.get(api_url, params=params)
                    if response.status_code == 200:
                        data = response.json()
                        if self.is_valid_product_response(data):
                            logger.info(f"找到有效的商品搜索API: {api_url}")
                            return data
                except Exception as e:
                    continue
        
        logger.warning("未找到有效的商品搜索API")
        return {}
    
    def get_shop_products(self, shop_id: str, page: int = 1) -> Dict:
        """获取店铺商品列表（推测的API）"""
        possible_shop_apis = [
            f"/gateway/shop/{shop_id}/products",
            f"/distribute/wireless/shop/products",
            f"/api/shop/product/list"
        ]
        
        for api_path in possible_shop_apis:
            api_url = f"{self.base_url}{api_path}"
            
            params_variations = [
                {"shopId": shop_id, "page": page},
                {"shop_id": shop_id, "pageNum": page},
                {"id": shop_id, "offset": (page-1)*20}
            ]
            
            for params in params_variations:
                try:
                    response = self.session.get(api_url, params=params)
                    if response.status_code == 200:
                        data = response.json()
                        if self.is_valid_product_response(data):
                            logger.info(f"找到有效的店铺商品API: {api_url}")
                            return data
                except Exception as e:
                    continue
        
        return {}
    
    def is_valid_product_response(self, data: Dict) -> bool:
        """判断是否为有效的商品响应"""
        if not isinstance(data, dict):
            return False
        
        # 检查常见的商品数据结构
        product_indicators = [
            'products', 'items', 'goods', 'list', 'data',
            'productList', 'itemList', 'goodsList'
        ]
        
        for indicator in product_indicators:
            if indicator in data:
                items = data[indicator]
                if isinstance(items, list) and len(items) > 0:
                    # 检查第一个项目是否包含商品字段
                    first_item = items[0]
                    if isinstance(first_item, dict):
                        product_fields = ['id', 'name', 'title', 'price', 'image']
                        if any(field in first_item for field in product_fields):
                            return True
        
        return False
    
    def extract_product_info(self, raw_data: Dict) -> List[Dict]:
        """从原始数据中提取商品信息"""
        products = []
        
        # 尝试不同的数据结构
        possible_data_paths = [
            ['data', 'list'],
            ['data', 'products'],
            ['result', 'items'],
            ['products'],
            ['items'],
            ['list']
        ]
        
        product_list = None
        for path in possible_data_paths:
            current = raw_data
            try:
                for key in path:
                    current = current[key]
                if isinstance(current, list):
                    product_list = current
                    break
            except (KeyError, TypeError):
                continue
        
        if not product_list:
            return products
        
        for item in product_list:
            if not isinstance(item, dict):
                continue
            
            # 提取商品信息
            product = {
                'id': self.safe_get(item, ['id', 'productId', 'itemId', 'goodsId']),
                'title': self.safe_get(item, ['title', 'name', 'productName', 'itemName']),
                'price': self.safe_get(item, ['price', 'currentPrice', 'salePrice']),
                'original_price': self.safe_get(item, ['originalPrice', 'marketPrice', 'listPrice']),
                'image': self.safe_get(item, ['image', 'imageUrl', 'pic', 'cover']),
                'sales': self.safe_get(item, ['sales', 'salesCount', 'soldCount']),
                'rating': self.safe_get(item, ['rating', 'score', 'rate']),
                'shop_name': self.safe_get(item, ['shopName', 'storeName', 'merchantName']),
                'description': self.safe_get(item, ['description', 'desc', 'summary']),
                'url': self.safe_get(item, ['url', 'link', 'detailUrl']),
                'crawl_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 只保留有效的商品
            if product['id'] and product['title']:
                products.append(product)
        
        return products
    
    def safe_get(self, data: Dict, keys: List[str]) -> Any:
        """安全获取字典值"""
        for key in keys:
            if key in data:
                return data[key]
        return None
    
    def run_analysis(self, postman_file: str):
        """运行API分析"""
        logger.info("开始分析抓包数据...")
        
        try:
            with open(postman_file, 'r', encoding='utf-8') as f:
                postman_data = json.load(f)
            
            # 提取API端点
            endpoints = self.extract_api_endpoints(postman_data)
            logger.info(f"提取到 {len(endpoints)} 个API端点")
            
            # 查找商品相关API
            product_apis = self.find_product_related_apis(endpoints)
            logger.info(f"发现 {len(product_apis)} 个可能的商品相关API")
            
            # 打印分析结果
            print("\n" + "="*60)
            print("API分析结果:")
            print("="*60)
            
            for i, api in enumerate(product_apis, 1):
                print(f"\n{i}. {api['name']}")
                print(f"   方法: {api['method']}")
                print(f"   路径: {api['path']}")
                
                if api['query']:
                    print("   查询参数:")
                    for param in api['query'][:3]:  # 只显示前3个参数
                        print(f"     - {param.get('key', '')}: {param.get('value', '')}")
                
                if api['body'].get('raw'):
                    try:
                        body_data = json.loads(api['body']['raw'])
                        print(f"   请求体: {json.dumps(body_data, ensure_ascii=False)[:100]}...")
                    except:
                        print(f"   请求体: {api['body']['raw'][:100]}...")
            
            # 尝试调用一些API
            print("\n" + "="*60)
            print("尝试调用API:")
            print("="*60)
            
            # 测试选品决策API
            print("\n1. 测试选品决策API...")
            decision_data = self.analyze_selection_decision_api()
            if decision_data:
                print(f"   响应数据: {json.dumps(decision_data, ensure_ascii=False)[:200]}...")
            
            # 测试PID绑定列表API
            print("\n2. 测试PID绑定列表API...")
            pid_data = self.get_user_pid_bind_list()
            if pid_data:
                print(f"   响应数据: {json.dumps(pid_data, ensure_ascii=False)[:200]}...")
            
            # 尝试搜索商品
            print("\n3. 尝试搜索商品...")
            search_data = self.search_products_by_keyword("手机")
            if search_data:
                products = self.extract_product_info(search_data)
                print(f"   找到 {len(products)} 个商品")
                if products:
                    print(f"   示例商品: {products[0]}")
            
        except Exception as e:
            logger.error(f"分析过程出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    spider = KuaishouAPISpider()
    
    # 分析抓包文件
    postman_file = "app.kwaixiaodian.com_07-16-2025-14-01-19.json"
    spider.run_analysis(postman_file)


if __name__ == "__main__":
    main()
